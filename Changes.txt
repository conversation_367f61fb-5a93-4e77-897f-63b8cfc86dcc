﻿———————————————————————————
  JoyToKey change history
———————————————————————————

Version 7.1.1
* Improved the key code emulation for Pause/Break key, and provided
  the alternative key code as well (accessible from the right-click menu).


Version 7.1.0
* If the buttons are pressed when the mapping changes due to
  SHIFT function or active profile switch, there was a feature to
  ignore the already pressed input "for the specified time".
  This feature was enhanced to be also able to ignore the input
  "until it's pressed again".
  This behavior can be configured form the "Preferences" menu.

* Added "Tab" key in the right-click assignment menu.

* Bug fix: When the same button's definition is triggered from multiple buttons,
  (e.g. <PERSON><PERSON>[20] is called from both LT and RT), it wasn't released at the 
  expected timing.

* Bug fix: Fixed the occasional display issue when "Options" tab was selected.


Version 7.0.0
* Support for the dark mode

* Support for high DPI display

* Support for button display template
  You can configure the button names like "A", "B, "X", "Y", ...
  instead of "Button 1", "Button 2", ...
  (From right-click menu, or from the device configuration menu)

* On the joystick tabs, device names will be displayed by default.
  Besides, users can also edit the device names for their convenience.
  (Right-click on the tab, and select "Edit the display name for this device")

* In the device configuration window, added the functionality to delete an old 
  (not connected) device configuration.


Version 6.9.3
* Bug fix for the mouse cursor movement to an absolute position.

* Other minor bug fix


Version 6.9.2
* Started to created a new build which doesn't use UNICODE arrow characters 
  (such as "←") in case they are not available on certain PC environments.

* Added a task tray right-click menu "Reset main window position to (0,0)".
  This can be useful in case JoyToKey window is lost somewhere and you want to
  forcibly bring it back to the screen.

* Renamed and re-ordered the right-click menu items during button assignment.

* Added POV in Button Mapping (Alias) function.

* Fixed the device detection in case X-Input device is removed out of order.

* Bug fix for "sqrt DOMAIN error" for certain controller.

* Bug fix for not being able to detect a certain controller at startup.


Version 6.9.1
* Bug fix for "Access Violation" error in certain PC environment.


Version 6.9
* Enhancement for the "Keyboard" tabs - Added the right-click menu to specify
  the mouse wheel input.  This enables the wheel input to be easily combined
  with other key inputs such as "ALT + wheel" or "CTRL + wheel" for zoom-in/out
  shortcut.

* Added the feature to trigger the custom vibration pattern when the active
  profile is switched (under the Options tab).
  NOTE: At this moment, it's limited to the first XInput device.

* Added the right-click menu to easily swap two button assignments.

* Added a few menu items for easier access (License key, donation and TIPs).

* Fixed the behavior when TAB key is pressed in "Keyboard Multi" setting
  window so that the basic configuration can be done without using a mouse.

* Fixed the "Notify via a sound" feature in Preference menu.


Version 6.8
* Enhanced "Configure diagonal input as separate buttons" feature such that
  it can be configured separately for left and right sticks.
  (Under "Options" tab -> Show/Hide Buttons)


Version 6.7.1
* Bug fix


Version 6.7
* Enhancement for the "Keyboard (multi)" tab - You can now trigger the 
  assignments on different buttons.  
  
  For example, let's assume that Button31 and Button32 are configured to 
  switch to different profiles.  Then, you can assign Button1 to
    Input1=Button31
    Input2=Button32 
  and switch them based on the button hold duration or based on how many 
  times it's pressed.

  NOTE: You can assign a different button function, by opening the right-click 
  context menu on the key assignment box, and select "Buttons" -> "Button 1~128".

* Enhanced the switch rotation in "Keyboard(Multi)" tab - Added an ability to 
  reset the counter when a button isn't pressed for a certain duration.  
  That means, when the button is pressed next time, it will go back to Input1.

* Enhanced the assignment based on the press duration in "Keyboard(Multi)" tab -
  It is now possible to assign an additional key "Input3" when the long-press is released.

* Updated the application icon - special thanks to Peter (Silent_ip)!

* When profiles (*.cfg files) exist in the same folder as "JoyToKey.exe", they'll
  take precedence over "JoyToKey" folder in user's Documents folder
  (except when JoyToKey is launched from "Programs Files (x86)" folder).

* Further improvement for "Experimental" mode (support triggers on certain case)

* Changed the spec for associating profiles with applications as follows:
  - if both path and title are specified, profile will switch only when both are matched
  - if only path is specified, profile will switch if path is matched
  - if only title is specified, profile will switch if title is matched

* When "internal Processing Speed" is changed from the preference, various timings
  (such as auto-repeat frequency, button hold duration) should no longer be affected.

* Bug fix: make the mouse movement consistent between Mouse tab and Keyboard tab.

* Bug fix: avoid the crash when changing the number of joysticks


Version 6.6
* Experimental support for a "Share" button on XBox Series X/S controller.
  In order to use a "Share" button, you may need to enable the experimental mode
  from menu "Settings" -> "Preferences", and change "Input device types" to
  "Experimental Logic (Beta)".

* Improved the automatic detection when an additional joystick is connected.

* Supported a negative coordinate for moving the mouse cursor to the absolute
  location.  It might be useful in case you want to jump to a monitor which is
  positioned left to the main monitor.

* Added a new command line option "-q" (quiet) to disable the log file generation.

* Added a new parameter in "JoyToKey.ini" file to default the configuration
  for newly detected devices for https://www.oneswitch.org.uk/

* Stopped the support for Windows XP, in order to use a new API.
  For those who are using Windows XP or 2000, DO NOT upgrade and 
  please continue to use the old version you have been using.

* Other minor bug fixes


Version 6.5
* New feature to paste a pre-defined text to the target application

* "Re-process when the same key continues while it's already pressed" option:

  If it is OFF, when the emulation of the same key continues (during sequence
  or from other buttons), that key will be only emulated once and kept hold.
  For example, if
    - Button1: SHIFT + keyA
    - Button2: SHIFT + keyB
  are both pressed together, SHIFT will be only emulated once, and will be kept 
  until both buttons are released.

  If the option is ON, SHIFT key will be pressed and released for each button,
  i.e. pressed and released twice in total.

* Minor bug fixes


Version 6.4.3
* Improved the association of a profile based on the target window title.

* Minor bug fixes


Version 6.4.2
* Minor bug fixes


Version 6.4.1
* Supported the association of profiles based on the target window title,
  besides the file path of the target application.

* ButtonAlias function - Support 3 button case

* Improved the compatibility when used from a launcher application


Version 6.4
* Supported repeat and toggle click in mouse absolute movement mode.


Version 6.3
* User configuration data will be by default stored in
  "JoyToKey" folder in user's "Documents" folder.

  In case you upgrade JoyToKey from an older version, please copy (or move)
  the old "JoyToKey" folder into your "Documents" folder (especially *.ini and *.cfg files)
  so that you can continue to access your configuration data.

* To be more friendly for new users,
  - JoyToKey is packaged as an installer, instead of a zip file format
  - Tab names in the button assignment window are made clearer
    e.g. "Keyboard 2" -> "Keyboard (Multi)"
  - UI Layout for assigning the mouse cursor movement is made easier

* Minor changes and bug fixes


Version 6.2
* Enhanced an execution of an external program to be able to
  - pass command parameters
  - specify the starting working directory

* In the button assignment window, added small buttons to go to
  the next and the previous assignment.
	
* In case JoyToKey is started when no controller is connected,
  JoyToKey tries to automatically detect devices until one
  controller is connected.

  But some users may want to continue the auto detection until
  two controllers are connected.  Or some user may want to
  disable this auto detection completely.  From this version,
  you can change the behaviour by changing the following line
  in "JoyToKey.ini" file.

  # in case you want to continue the auto-detection until two devices
  AutoDetectDevicesUpTo=2

  # in case you want to disable the auto-detection
  AutoDetectDevicesUpTo=0

  NOTE: Repeated auto-detection of devices while playing the game may
  lead to a slight delay or input lag occasionally.  So this auto
  detection should be only used for the number of devices which are
  always connected.

* Support up to max 50 joysticks (previously 32 joysticks)

* Support up to max 128 buttons (previously 32 buttons)


Version 6.1.1
* Supported multiple displays for the absolute movement of mouse cursor.

* Important bug fix for version 6.x


Version 6.1
* Supported an execution of an external program for a button assignment
  Besides launching a program, you can also open an URL in the browser.
  These functions enable you to use JoyToKey as a kind of launcher.

* Supported a command line argument "-r"
  When new joysticks are connected, there are a few ways to make
  JoyToKey to recognize them. (e.g. one easy way is to click a JoyToKey
  icon in the task tray)  This command line argument is another way to
  trigger that: you can create a shortcut to execute "JoyToKey.exe -r",
  and it'll force the already-running JoyToKey instance to refresh the
  list of joystick devices.


Version 6.0
* Supported additional key-code emulations such as the volume control,
  media control (prev / next / play / stop), and SCROLL_LOCK.

* Changed the default key emulation code for arrow keys, R-Alt and
  R-Ctrl, to fix compatibility issue with recent Windows 10.
  In case it causes a problem for certain games and you want to 
  use the old key code, please select an equivalent key (from
  right-click menu) which is labeled as "for DirectInput".

* Refresh joystick connections whenever a task tray icon is clicked.
  When you connect a new joystick to the PC, just click the task tray
  icon to get it recognized (either left click or right click).

* Ability to re-process the key emulation event when the same key 
  continues during the processing of Input1-Input4 sequence (Keyboard 2).

  By default, when processing the sequence of Input1 through Input4,
  if the same key assignment continues, it's emulated only once.
  In the example below, key "↓" will be only pressed once and will
  be kept pressed until Input2 is released.

  Input1 = "↓"
  Input2 = "↓" + "→"

  This behaviour can be changed from "Options" tab with the option:
  "Re-process when the same key continues in sequence"

* Misc bug fixes such as button highlighting


Version 5.9
* Fixed a crash issue for some online games.

* Enhanced "Keyboard2" page to support the followings:

  - Supported the emulation of Input1 ~ Input4 in sequence at
    the specified interval (hold duration).

  - Supported different key assignment based on how many times
    a button is pressed within the specified time threshold.

  - Supported the key assignment when a button is released.

  - Supported the definition of mouse cursor movement via right click
    menu.  For example, this enables one button input to cycle through
    up, right, down, left mouse cursor movement whenever it's pressed.

* Supported a mouse horizontal wheel rotation

* Improved the keycode emulation for the Numpad '/' and a few
  other keys.

* Miscellaneous configuration options were added for OneSwitch.org.uk
  ex)
  FontSizeProfileList=20
  FontSizeButtonList=20
  ProfileImageFullScreenRatio=1.0


Version 5.8.2
* Increased the maximum number of joysticks from 16 to 32.


Version 5.8.1
* Bug fix


Version 5.8
* Enhanced the support for XBox360 controller.  For example,
  - The silver guide button can be assigned as button 13
  - Left/Right trigger can be assigned as analog input button 11, 12

* Added support for XInput devices besides DirectInput devices.

* Enhanced auto-repeat definition to support more flexible definition.
  (for example, 1.2 times per second, or repeat every 2.5 second)

* Added an ability to show an image file when an active profile is changed.
  Image file can be registered in Options tab for each profile.

* A few minor bug fixes


Version 5.7.1
* Support "Numpad Enter" key assignment via right-click menu.

* Support up to 4 POV inputs.

* Enhanced the right-click menu on the joystick tab and on the button list
  such that the selected configuration can be copied-to or pasted-from
  a clipboard.

* Enhanced device configuration to support axis mapping for all 8 axes.


Version 5.7
* Support for an absolute mouse movement (a.k.a. spring mode)
  Mouse cursor can be positioned at a configured center location and can
  be moved within a specified range with an analog input stick. This can
  be useful for an application like a flight simulator easier.

  A regular button can be also used like a mouse shortcut by jumping the
  mouse cursor to a preconfigured position, followed by a mouse click.

* Exposed hidden parameter "AnalogDeadZone" (which is used for DirectInput
  setup) in "Settings -> Configure Joysticks". If you want to minimize the
  input threshold for your device, please try setting it to zero. If you
  need a different threshold for Stick1 and Stick2, you can continue to do
  so in "Options" tab for each profile.

* Enhanced SHIFT key (virtual joystick) function to support delayed switch.
  Combined with a "Button Number Mapping" feature, for example, one button
  can be assigned for both a regular key input (for a short press) as well
  as for a SHIFT function (for a long press).

* Enhanced "Switch depending on how long the button is pressed" in Keyboard2.
  In case the event emulation is not recognized reliably when you press
  the button too quickly, JoyToKey can now hold it for a minimum duration.
  (default 20 msec)

* Other minor bug fixes


Version 5.6
* Enhanced "Keyboard 2" functionality to support the followings
  - support 4 keys per definition
  - support the rotation of 4 input definitions
  - support auto repeat and toggle configuration

* Upgraded DirectX library version from 7 to 8.


Version 5.5.x
* bug fixes


Version 5.5
* Support "Button Alias (Number Mapping)"

  Example Usage1:
    Swap button numbers if the connected joystick has a different button
    layout from your favorite one.

  Example Usage2:
    Associate Button21 with Button1 so that both functions are triggered
    when Button1 is pressed.

  Example Usage3:
    It is also possible to configure some button (e.g. Button22) to be
    triggered when both Button5 and Button6 are pressed simultaneously.

* If some buttons are pressed when profile is changed or SHIFT function
  is triggered, those button press are likely to trigger both old and new
  assignments right before/after the switch.

  It is now possible to define certain duration (e.g. 100 msec) to ignore
  those inputs right after the switch (from preference menu)

* When both mouse cursor movement and mouse click are assigned to a button,
  click&hold will be processed before cursor movement.

* Improved the reliability after resuming from PC sleep.

* Preferences were moved from menu items to a separate window.

* Added an ability to specify the number of buttons to configure.
  (by default 16 buttons, MAX 32 buttons)

* Changed the user interface font to Calibri

* Configuration files are now saved in Unicode format (UTF8+BOM).
  This may fix garbled character issues for the use of non-default
  language.


Version 5.4.2
* Fixed garbled characters for some non-Japanese PC and keyboard.

* (only for advanced users)
  Support specifying "JoyToKey.ini" file path via registry.
  If you have any reason why you need to forcibly specify "JoyToKey.ini"
  file path (e.g. you're using some launcher like Hyperlaunch), you can
  do so via registry editor.
  1. Run "regedit" program
  2. Create a key "HKEY_CURRENT_USER\Software\JoyToKey"
  3. Create a string value "IniFilePath=c:\whateverpath\JoyToKey.ini"


Version 5.4.1
* Support various file formats (such as mp3) for sound notifications.


Version 5.4
* Added an ability to play sound or show tasktray balloon when the
  selected profile is changed.

  - Tasktray balloon can be enabled from "Preferences" menu.

  - Sound notification can be configured for each profile in "Options"
    tab. (currently, only WAV files are supported)

  For example, sound notification could be useful for visually impaired
  users, while they step through different profiles to help them to pick
  the right one.

* The size of profile files is made smaller by eliminating empty lines.

* In case "JoyToKey.ini" file or its directory is not write-able,
  JoyToKey will try to make them write-able.


Version 5.3.1
* Homepage URL was changed to http://joytokey.net/


Version 5.3.0
* Support delayed input for a special function to switch profiles.
  If you want to keep JoyToKey running while you're playing a game
  which has a native joystick support, the followings may be useful.
  - Temporarily switch to a profile, which is basically empty
  - But it has one button assigned for switching back to the
    original profile after being pressed for X seconds.

* Control the behaviour when JoyToKey window is active from menu
  "Preferences" ->
    "Don't emulate keyboard input when JoyToKey window is active"
  (by default true)


Version 5.2.3
* Support emulation for 4th and 5th mouse button.
  They could be useful if you're using mouse gestures with joysticks.

* Turbo mode was enhanced to support up to x16 processing speed.
  However, this is experimental and should not be used for regular use
  as it's very CPU intensive and is likely to slow down the PC.


Version 5.2.2
* Support for loading configuration files from a common AppData folder
  when it exists. (e.g. C:\ProgramData\JoyToKey)

* Added a new menu "Open file folder in Explorer"


Version 5.2.1
* Bug fix: garbled characters for allow keys


Version 5.2.0
* Support the combination of keyboard and mouse click
  Ex) SHIFT+Click, CTRL+Click

  In order to specify a mouse click in keyboard assignment window,
  please right-click the entry box.

* Bug fix: minor bug fix for 5.1.2


Version 5.1.2
* Bug fix: SHIFT + arrow keys were not properly processed.


Version 5.1.1
* Mouse cursor emulation with analog sticks is further improved.


Version 5.1.0
* Mouse cursor emulation is now much smoother with analog sticks,
  by using a continuous acceleration without a threshold.

* In case analog stick input has been wrong, axis mapping can be
  manually specified.
  (Menu: "Preferences" -> "Configure joysticks")

* In case you have multiple joysticks and want to always use one
  of them as "Joystick 2" (or any other joystick number), you can
  define a priority/preferred number for each device.
  (Menu: "Preferences" -> "Configure joysticks")

* Miscellaneous bug fixes.


Version 5.0.3
* (Improvement) Re-detect joysticks after resuming from sleep.


Version 5.0.2
* Bug fix: Preference "Highlight buttons when JoyToKey is active" was not
  properly saved.


Version 5.0.1
* Added a menu "Help -> Overview"
* Added a button "Edit button assignment"
* Minor bug fixes


Version 5.0
* Support for different key assignments based on analog stick input level.

* Support for different key assignments based on button hold duration.

* Support for rotating key assignments.  For example, whenever the same
  button is pressed, its key assignment can rotate like 
  "A -> B -> A -> B -> A ..."

* Enhanced auto-repeat key functionality.
  The following auto-repeat definitions are newly possible:
  - "1.5 repeats per second"
  - "one repeat per 123.4 seconds"
  - "auto-repeat only after 500 milli-seconds"

* When the main window of JoyToKey is activated and joystick buttons are
  pressed, those buttons will be highlighted in the main window so that
  you can easily see which button corresponds to which row in the config.
  (You can change it back to the old behaviour from menu setting)

* When the window is minimized, icon will be kept in the taskbar by default.
  If you prefer the old behavoir and want to hide it from taskbar, you can
  change it back to the old behavior from settings menu.

* Added a comment field for each button definition.
  For example, you can record a memo like "This button is used for Jump".

* Support for copying joystick configration from one to another.
  Right-click "Joystick 1" header in the main menu, to open a popup menu.

* Support for at maximum 4 key assignments for each button.

* Various small bug fixes


Version 4.6
* You can choose the default behavior when the target application switches 
  to non-associated appliation.

* When you temporarily switch to the virtual joystick configuration while 
  some button is being pressed, the pressed state will be kept without 
  release event if its original key config and the new key config are the
  same.  (e.g. if the arrow key is assigned for both the original joystick1 
  config and the virtual joystick3 config, then switching between joystick1 
  and joystick3 won't trigger unnecessary release event)


Version 4.x
* It became a shareware soft

* Support for associating config files with applications
  It can automatically switch active configuration file based on running 
applications.

* Support dynamic acceleration of mouse cursor movement
  If you use mouse cursor emulation with analog stick, the speed 
sensitivity can be accelerated at certain threshold.

* Support 4-way and 8-way assignment for POV control

* Auto repeat functionality, ranging from very slow repeat (1 time per 
15 minutes) up to 60 times per second (with 2x processing mode)

* Support for toggle button
  e.g. use it for drag&drop with mouse emulation, or for truly automated 
repeat of keys even when you are away from PC.

* Window size became re-sizable

* Open / Close main window by left-clicking task tray icon 
  (right-click to open popup menu)

* JoyToKey can be started even when joystick is not connected
  And it will automatically detect the newly connected joystick.
  (NOTE: it only works the first joystick.  If you connect the second one
   or more, please press "detect" button manually from preferences panel)

* Several additions in the menu such as
  - copy configuration file
  - open game controller setting from control panel
  - suspend / resume temporalily
  - 2x processing mode (if you want to minimize the input latency, sacrificing extra CPU load)
  - generate debug log


Version 3.x
* Under Windows 2000 or later (including XP), JoyToKey now supports
  some applications using DirectInput.

* Choosing L-Shift, R-Shift, L-Ctrl, ... from the list, you can make
  a distinction between left and right keys.
  (Be careful to select L-Shift or R-Shift or (normal) Shift!
   You have to choose the correct one for the application!)

* Support for POV switches.

* Support for "Switch to the other configuration file" function.

* The configuration file can be easily selected and switched to
  at task tray menu, without opening and activating JoyToKey window.

* The firstly activated configuration file can now be specified
  by command line option at start up time.

* Analog stick can be configured in detail by modifying JoyToKey.ini
  file directly:

  "AnalogDeadZone"  (ranges 0 to 10000: default 1000)
    The center area where subtle input from analog stick is ignored.

  "AnalogSaturation"  (ranges 0 to 10000: default 10000)
    The surrounding area from which additional inputs are saturated.
