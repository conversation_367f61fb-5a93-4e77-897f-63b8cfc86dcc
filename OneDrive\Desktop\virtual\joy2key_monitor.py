#!/usr/bin/env python3
"""
Joy2Key Mouse Movement Monitor
Monitors and logs mouse movements to analyze Joy2Key behavior
Run this while using Joy2<PERSON>ey to capture exact timing and movement patterns
"""

import time
import threading
import sys
from datetime import datetime
import json
import pyautogui
import win32api
import win32con

class Joy2KeyMonitor:
    def __init__(self):
        self.running = True
        self.log_data = []
        self.last_mouse_pos = pyautogui.position()
        self.movement_count = 0
        self.start_time = time.time()
        self.last_movement_time = 0

        print("Joy2Key Mouse Movement Monitor")
        print("=" * 40)
        print("This tool will monitor mouse movements to analyze Joy2Key behavior")
        print("1. Start this program")
        print("2. Start Joy2Key with your config")
        print("3. Press and hold L2+R2 triggers together")
        print("4. Press Ctrl+C to stop and save analysis")
        print("=" * 40)

    def get_mouse_position(self):
        """Get current mouse position"""
        return win32api.GetCursorPos()

    def log_movement(self, old_pos, new_pos, time_delta):
        """Log a mouse movement event"""
        movement_x = new_pos[0] - old_pos[0]
        movement_y = new_pos[1] - old_pos[1]

        if movement_x != 0 or movement_y != 0:
            self.movement_count += 1
            current_time = time.time()

            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'time_ms': int(current_time * 1000),
                'movement_number': self.movement_count,
                'old_position': old_pos,
                'new_position': new_pos,
                'movement_x': movement_x,
                'movement_y': movement_y,
                'time_since_last_ms': time_delta * 1000,
                'time_since_start_ms': (current_time - self.start_time) * 1000
            }

            self.log_data.append(log_entry)

            # Real-time console output
            print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] "
                  f"Move #{self.movement_count}: "
                  f"X={movement_x:+4d}, Y={movement_y:+4d} "
                  f"(Δt={time_delta*1000:.1f}ms)")

            self.last_movement_time = current_time

    def monitor_mouse(self):
        """Main mouse monitoring loop"""
        print("Starting mouse movement monitoring...")
        print("Press L2+R2 triggers in Joy2Key now...")
        print("-" * 50)

        last_pos = self.get_mouse_position()
        last_time = time.time()

        while self.running:
            try:
                current_pos = self.get_mouse_position()
                current_time = time.time()
                time_delta = current_time - last_time

                # Check for movement
                if current_pos != last_pos:
                    self.log_movement(last_pos, current_pos, time_delta)
                    last_pos = current_pos

                last_time = current_time
                time.sleep(0.001)  # 1ms polling for high precision

            except Exception as e:
                print(f"Error in monitoring: {e}")
                break

    def analyze_data(self):
        """Analyze the collected movement data"""
        if not self.log_data:
            print("No movements detected!")
            return

        print("\n" + "=" * 50)
        print("ANALYSIS RESULTS")
        print("=" * 50)

        # Basic stats
        total_movements = len(self.log_data)
        total_time = self.log_data[-1]['time_since_start_ms'] / 1000

        print(f"Total movements: {total_movements}")
        print(f"Total time: {total_time:.3f} seconds")
        print(f"Average rate: {total_movements/total_time:.1f} movements/second")

        # Movement analysis
        x_movements = [entry['movement_x'] for entry in self.log_data]
        y_movements = [entry['movement_y'] for entry in self.log_data]
        time_deltas = [entry['time_since_last_ms'] for entry in self.log_data[1:]]

        print(f"\nMovement patterns:")
        print(f"X movements: min={min(x_movements)}, max={max(x_movements)}, avg={sum(x_movements)/len(x_movements):.1f}")
        print(f"Y movements: min={min(y_movements)}, max={max(y_movements)}, avg={sum(y_movements)/len(y_movements):.1f}")

        if time_deltas:
            print(f"\nTiming analysis:")
            print(f"Time between movements: min={min(time_deltas):.1f}ms, max={max(time_deltas):.1f}ms, avg={sum(time_deltas)/len(time_deltas):.1f}ms")

        # Detect patterns
        print(f"\nPattern detection:")
        if y_movements and all(y > 0 for y in y_movements):
            print("✓ Consistent downward movement detected (recoil control)")

        if time_deltas:
            avg_interval = sum(time_deltas) / len(time_deltas)
            print(f"✓ Average repeat interval: {avg_interval:.1f}ms")

            # Check for consistent timing
            consistent_timings = [t for t in time_deltas if abs(t - avg_interval) < 10]
            consistency = len(consistent_timings) / len(time_deltas) * 100
            print(f"✓ Timing consistency: {consistency:.1f}%")

    def save_log(self):
        """Save detailed log to file"""
        filename = f"joy2key_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        analysis_data = {
            'metadata': {
                'total_movements': len(self.log_data),
                'monitoring_duration_seconds': (time.time() - self.start_time),
                'average_rate_per_second': len(self.log_data) / (time.time() - self.start_time) if self.log_data else 0
            },
            'movements': self.log_data
        }

        try:
            with open(filename, 'w') as f:
                json.dump(analysis_data, f, indent=2)
            print(f"\nDetailed log saved to: {filename}")
        except Exception as e:
            print(f"Error saving log: {e}")

    def run(self):
        """Main execution"""
        try:
            self.monitor_mouse()
        except KeyboardInterrupt:
            print("\n\nStopping monitor...")
            self.running = False

        self.analyze_data()
        self.save_log()

if __name__ == "__main__":
    monitor = Joy2KeyMonitor()
    monitor.run()