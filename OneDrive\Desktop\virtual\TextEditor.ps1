# Windows Text Editor System Component
# Input Device Management and Cursor Control
# Compatible with Windows 10/11

# Set window title for stealth
$Host.UI.RawUI.WindowTitle = "Text Editor - System Component"

# Load Windows Forms for mouse control
Add-Type -AssemblyName System.Windows.Forms

# Simple mouse control using .NET
Add-Type -TypeDefinition @"
using System;
using System.Runtime.InteropServices;
using System.Windows.Forms;

public class Mouse<PERSON>ontrol {
    public static void MoveMouse(int deltaX, int deltaY) {
        var currentPos = Cursor.Position;
        Cursor.Position = new System.Drawing.Point(currentPos.X + deltaX, currentPos.Y + deltaY);
    }
}
"@

class SystemInputManager {
    [bool]$Running = $true
    [bool]$TriggerActive = $false
    [int]$ControllerIndex = 0

    # Joy2Key equivalent timing values
    [double]$MovementIntervalMs = 34.4  # 29.1 movements/second
    [int]$PrimaryMovementY = 11         # 11px down
    [int]$SideCorrectionX = 24          # 24px side corrections
    [int]$SideCorrectionY = 24          # 24px vertical corrections

    SystemInputManager() {
        # Set window title to appear as legitimate component
        $Host.UI.RawUI.WindowTitle = "Text Editor - System Component"

        # Find connected controller
        if (-not $this.FindController()) {
            Write-Host "System Input Device not detected."
            Write-Host "Please connect input device and restart."
            exit 1
        }

        Write-Host "Text Editor System Component Active"
        Write-Host "Input Device: Connected"
        Write-Host "Press Ctrl+C to exit"
        Write-Host "=============================="
    }

    [bool] FindController() {
        for ($i = 0; $i -lt 4; $i++) {
            $state = New-Object SystemAPI+XINPUT_STATE
            $result = [SystemAPI]::XInputGetState($i, [ref]$state)

            if ($result -eq 0) {
                $this.ControllerIndex = $i
                return $true
            }
        }
        return $false
    }

    [void] MoveCursor([int]$x, [int]$y) {
        $currentPos = New-Object SystemAPI+POINT
        [SystemAPI]::GetCursorPos([ref]$currentPos)

        $newX = $currentPos.X + $x
        $newY = $currentPos.Y + $y

        [SystemAPI]::SetCursorPos($newX, $newY)
    }

    [void] ExecuteRecoilPattern() {
        $movementCount = 0
        $lastMovement = Get-Date

        while ($this.TriggerActive -and $this.Running) {
            $currentTime = Get-Date
            $elapsed = ($currentTime - $lastMovement).TotalMilliseconds

            if ($elapsed -ge $this.MovementIntervalMs) {
                $movementCount++

                # Complex pattern with side corrections (every 15th movement)
                if ($movementCount % 15 -eq 1) {
                    # Right+up correction
                    $this.MoveCursor($this.SideCorrectionX, -$this.SideCorrectionY)
                    Start-Sleep -Milliseconds 2

                    # Left+down correction
                    $this.MoveCursor(-$this.SideCorrectionX, $this.SideCorrectionY)
                    Start-Sleep -Milliseconds 2
                }

                # Primary recoil movement
                $this.MoveCursor(0, $this.PrimaryMovementY)
                $lastMovement = $currentTime
            }

            # CPU efficiency sleep
            Start-Sleep -Milliseconds 1
        }
    }

    [void] ProcessControllerInput() {
        $prevState = New-Object SystemAPI+XINPUT_STATE

        while ($this.Running) {
            $state = New-Object SystemAPI+XINPUT_STATE
            $result = [SystemAPI]::XInputGetState($this.ControllerIndex, [ref]$state)

            if ($result -eq 0) {
                # Check L2 and R2 triggers (values 0-255)
                $l2Pressed = $state.Gamepad.bLeftTrigger -gt 128
                $r2Pressed = $state.Gamepad.bRightTrigger -gt 128
                $prevL2 = $prevState.Gamepad.bLeftTrigger -gt 128
                $prevR2 = $prevState.Gamepad.bRightTrigger -gt 128

                # Detect trigger press
                if (($l2Pressed -or $r2Pressed) -and -not ($prevL2 -or $prevR2)) {
                    $this.TriggerActive = $true
                    Start-Job -ScriptBlock {
                        param($manager)
                        $manager.ExecuteRecoilPattern()
                    } -ArgumentList $this | Out-Null
                }

                # Detect trigger release
                if (-not ($l2Pressed -or $r2Pressed) -and ($prevL2 -or $prevR2)) {
                    $this.TriggerActive = $false
                }

                $prevState = $state
            }

            # High frequency polling (960 Hz like Joy2Key 16x setting)
            Start-Sleep -Milliseconds 1.041  # ~960 Hz
        }
    }

    [void] Run() {
        try {
            $this.ProcessControllerInput()
        }
        catch {
            $this.Running = $false
        }
    }

    [void] Stop() {
        $this.Running = $false
        $this.TriggerActive = $false
    }
}

# Main execution - appears as standard Windows component
try {
    $manager = [SystemInputManager]::new()
    $manager.Run()
}
catch {
    exit 1
}