#!/usr/bin/env python3
"""
Direct Joy2Key Replacement for Recoil Control
Replicates the exact behavior from your j2k_apex.cfg:
- Button20: Mouse down 45 pixels (19ms delay)
- Button21: Mouse right 100px + up 100px
- Button22: Mouse left 100px + down 100px
"""

import pygame
import pyautogui
import time
import threading
import sys
from datetime import datetime

class RecoilController:
    def __init__(self):
        pygame.init()
        pygame.joystick.init()

        # Disable pyautogui failsafe for gaming
        pyautogui.FAILSAFE = False
        pyautogui.PAUSE = 0  # Remove default pause between commands

        self.running = True
        self.button_states = {}

        # Initialize joysticks
        self.joysticks = []
        joystick_count = pygame.joystick.get_count()

        for i in range(joystick_count):
            joystick = pygame.joystick.Joystick(i)
            joystick.init()
            self.joysticks.append(joystick)
            print(f"Controller {i}: {joystick.get_name()}")

        if not self.joysticks:
            print("ERROR: No controllers detected!")
            print("Please connect your controller and try again.")
            sys.exit(1)

        print("Recoil Controller Active!")
        print("L2/R2 Triggers: Recoil Control")
        print("Press Ctrl+C to exit")
        print("=" * 30)

    def move_mouse_button20(self):
        """Button20: Move mouse down 45 pixels with 19ms delay"""
        try:
            # Joy2Key parameters: 2, 0, 45, 0, 0, 0, 0, 19.000, 0, 95, 0.1, 100
            # Mode 2 = mouse movement, X=0, Y=45, delay=19ms
            time.sleep(0.019)  # 19ms delay
            pyautogui.moveRel(0, 45)  # Move down 45 pixels
            print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] Button20: Mouse down 45px")
        except Exception as e:
            print(f"Error in button20 movement: {e}")

    def move_mouse_button21(self):
        """Button21: Move mouse right 100px and up 100px"""
        try:
            # Joy2Key parameters: 2, 100, -100, 0, 0, 0, 0, 44.000, 0, 95, 1.0, 100
            # Mode 2 = mouse movement, X=100, Y=-100, delay=44ms
            time.sleep(0.044)  # 44ms delay
            pyautogui.moveRel(100, -100)  # Right 100px, Up 100px
            print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] Button21: Mouse right+up 100px")
        except Exception as e:
            print(f"Error in button21 movement: {e}")

    def move_mouse_button22(self):
        """Button22: Move mouse left 100px and down 100px"""
        try:
            # Joy2Key parameters: 2, -100, 100, 0, 0, 0, 0, 44.000, 0, 95, 1.0, 100
            # Mode 2 = mouse movement, X=-100, Y=100, delay=44ms
            time.sleep(0.044)  # 44ms delay
            pyautogui.moveRel(-100, 100)  # Left 100px, Down 100px
            print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] Button22: Mouse left+down 100px")
        except Exception as e:
            print(f"Error in button22 movement: {e}")

    def handle_button_press(self, joystick_id, button_id):
        """Handle button press events"""
        button_key = f"joy{joystick_id}_btn{button_id}"

        if button_key in self.button_states and self.button_states[button_key]:
            return  # Button already pressed, avoid duplicate actions

        self.button_states[button_key] = True

        # PS4 Controller mapping: Button 0 = R2, Button 2 = L2
        if button_id in [0, 2]:  # L2 or R2 trigger
            threading.Thread(target=self.continuous_recoil_control, args=(button_key,), daemon=True).start()

    def handle_button_release(self, joystick_id, button_id):
        """Handle button release events"""
        button_key = f"joy{joystick_id}_btn{button_id}"
        self.button_states[button_key] = False

    def continuous_recoil_control(self, button_key):
        """Recoil control matching Joy2Key's timing and pattern"""
        movement_count = 0
        last_movement_time = time.time()

        while self.button_states.get(button_key, False) and self.running:
            try:
                current_time = time.time()
                time_since_last = (current_time - last_movement_time) * 1000

                if time_since_last >= 34.4:  # 34.4ms interval to match Joy2Key's 29.1/sec rate
                    movement_count += 1

                    # Complex pattern with occasional side corrections
                    if movement_count % 15 == 1:
                        pyautogui.moveRel(24, -24)  # Right+up
                        time.sleep(0.002)
                        pyautogui.moveRel(-24, 24)  # Left+down
                        time.sleep(0.002)

                    # Primary recoil movement
                    pyautogui.moveRel(0, 11)  # 11px down
                    last_movement_time = current_time

                time.sleep(0.001)  # CPU efficiency

            except Exception as e:
                break

    def check_trigger_combination(self):
        """Check if both L2 and R2 triggers are pressed"""
        l2_pressed = self.button_states.get("joy0_btn2", False)
        r2_pressed = self.button_states.get("joy0_btn0", False)
        return l2_pressed and r2_pressed

    def run(self):
        """Main controller monitoring loop"""
        clock = pygame.time.Clock()

        try:
            while self.running:
                for event in pygame.event.get():
                    if event.type == pygame.QUIT:
                        self.running = False
                    elif event.type == pygame.JOYBUTTONDOWN:
                        self.handle_button_press(event.joy, event.button)
                    elif event.type == pygame.JOYBUTTONUP:
                        self.handle_button_release(event.joy, event.button)

                clock.tick(960)  # 16x processing speed

        except KeyboardInterrupt:
            self.running = False
        finally:
            pygame.quit()

def main():
    """Main entry point"""
    print("Joy2Key Replacement - Recoil Controller")
    print("=" * 40)

    try:
        controller = RecoilController()
        controller.run()
    except Exception:
        sys.exit(1)

if __name__ == "__main__":
    main()