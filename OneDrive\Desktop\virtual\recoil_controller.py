#!/usr/bin/env python3
"""
Direct Joy2Key Replacement for Recoil Control
Replicates the exact behavior from your j2k_apex.cfg:
- Button20: Mouse down 45 pixels (19ms delay)
- Button21: Mouse right 100px + up 100px
- Button22: Mouse left 100px + down 100px
"""

import pygame
import pyautogui
import time
import threading
import sys
from datetime import datetime

class RecoilController:
    def __init__(self):
        pygame.init()
        pygame.joystick.init()

        # Disable pyautogui failsafe for gaming
        pyautogui.FAILSAFE = False
        pyautogui.PAUSE = 0  # Remove default pause between commands

        self.running = True
        self.button_states = {}

        # Initialize joysticks
        self.joysticks = []
        joystick_count = pygame.joystick.get_count()
        print(f"DEBUG: Found {joystick_count} controller(s)")

        for i in range(joystick_count):
            joystick = pygame.joystick.Joystick(i)
            joystick.init()
            self.joysticks.append(joystick)
            print(f"Controller {i}: {joystick.get_name()}")
            print(f"  - Buttons: {joystick.get_numbuttons()}")
            print(f"  - Axes: {joystick.get_numaxes()}")
            print(f"  - Hats: {joystick.get_numhats()}")

        if not self.joysticks:
            print("ERROR: No controllers detected!")
            print("Please connect your controller and try again.")
            print("Make sure your controller is properly connected and recognized by Windows.")
            sys.exit(1)

        print("Recoil Controller Active!")
        print("Processing Speed: 16x (960 Hz) - matching Joy2Key settings")
        print("Button mappings:")
        print("- Button 20: Mouse down 45px (recoil control)")
        print("- Button 21: Mouse right 100px + up 100px")
        print("- Button 22: Mouse left 100px + down 100px")
        print("Press Ctrl+C to exit")
        print("=" * 50)

    def move_mouse_button20(self):
        """Button20: Move mouse down 45 pixels with 19ms delay"""
        try:
            # Joy2Key parameters: 2, 0, 45, 0, 0, 0, 0, 19.000, 0, 95, 0.1, 100
            # Mode 2 = mouse movement, X=0, Y=45, delay=19ms
            time.sleep(0.019)  # 19ms delay
            pyautogui.moveRel(0, 45)  # Move down 45 pixels
            print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] Button20: Mouse down 45px")
        except Exception as e:
            print(f"Error in button20 movement: {e}")

    def move_mouse_button21(self):
        """Button21: Move mouse right 100px and up 100px"""
        try:
            # Joy2Key parameters: 2, 100, -100, 0, 0, 0, 0, 44.000, 0, 95, 1.0, 100
            # Mode 2 = mouse movement, X=100, Y=-100, delay=44ms
            time.sleep(0.044)  # 44ms delay
            pyautogui.moveRel(100, -100)  # Right 100px, Up 100px
            print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] Button21: Mouse right+up 100px")
        except Exception as e:
            print(f"Error in button21 movement: {e}")

    def move_mouse_button22(self):
        """Button22: Move mouse left 100px and down 100px"""
        try:
            # Joy2Key parameters: 2, -100, 100, 0, 0, 0, 0, 44.000, 0, 95, 1.0, 100
            # Mode 2 = mouse movement, X=-100, Y=100, delay=44ms
            time.sleep(0.044)  # 44ms delay
            pyautogui.moveRel(-100, 100)  # Left 100px, Down 100px
            print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] Button22: Mouse left+down 100px")
        except Exception as e:
            print(f"Error in button22 movement: {e}")

    def handle_button_press(self, joystick_id, button_id):
        """Handle button press events"""
        print(f"DEBUG: Button {button_id} pressed on joystick {joystick_id}")
        button_key = f"joy{joystick_id}_btn{button_id}"

        if button_key in self.button_states and self.button_states[button_key]:
            print(f"DEBUG: Button {button_id} already pressed, ignoring duplicate")
            return  # Button already pressed, avoid duplicate actions

        self.button_states[button_key] = True

        # Execute the appropriate mouse movement based on button
        if button_id == 20:
            print(f"DEBUG: Starting continuous recoil control for button 20")
            # Start continuous movement for recoil control
            threading.Thread(target=self.continuous_button20, daemon=True).start()
        elif button_id == 21:
            print(f"DEBUG: Executing button 21 movement")
            threading.Thread(target=self.move_mouse_button21, daemon=True).start()
        elif button_id == 22:
            print(f"DEBUG: Executing button 22 movement")
            threading.Thread(target=self.move_mouse_button22, daemon=True).start()
        else:
            print(f"DEBUG: Button {button_id} pressed but not mapped to any action")

    def handle_button_release(self, joystick_id, button_id):
        """Handle button release events"""
        print(f"DEBUG: Button {button_id} released on joystick {joystick_id}")
        button_key = f"joy{joystick_id}_btn{button_id}"
        self.button_states[button_key] = False

        if button_id == 20:
            print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] Button20 released - stopping recoil")
        elif button_id in [21, 22]:
            print(f"DEBUG: Button {button_id} released")

    def continuous_button20(self):
        """Continuous mouse movement for Button20 while held"""
        button_key = f"joy0_btn20"  # Assuming first joystick

        while self.button_states.get(button_key, False) and self.running:
            try:
                # Joy2Key repeat rate: 0.1 (from config), so 100ms between repeats
                pyautogui.moveRel(0, 45)  # Move down 45 pixels
                time.sleep(0.1)  # 100ms repeat interval
            except Exception as e:
                print(f"Error in continuous movement: {e}")
                break

    def run(self):
        """Main controller monitoring loop"""
        clock = pygame.time.Clock()
        event_count = 0
        last_status_time = time.time()

        print("DEBUG: Starting main controller loop...")
        print("DEBUG: Press any button on your controller to test detection")

        try:
            while self.running:
                events = pygame.event.get()
                if events:
                    event_count += len(events)

                for event in events:
                    if event.type == pygame.QUIT:
                        print("DEBUG: Quit event received")
                        self.running = False

                    elif event.type == pygame.JOYBUTTONDOWN:
                        self.handle_button_press(event.joy, event.button)

                    elif event.type == pygame.JOYBUTTONUP:
                        self.handle_button_release(event.joy, event.button)

                    elif event.type == pygame.JOYAXISMOTION:
                        # Log significant axis movements for debugging
                        if abs(event.value) > 0.5:
                            print(f"DEBUG: Axis {event.axis} = {event.value:.3f} on joystick {event.joy}")

                    elif event.type == pygame.JOYHATMOTION:
                        print(f"DEBUG: Hat {event.hat} = {event.value} on joystick {event.joy}")

                # Status update every 5 seconds
                current_time = time.time()
                if current_time - last_status_time > 5.0:
                    print(f"DEBUG: Still running... Events processed: {event_count}")
                    last_status_time = current_time
                    event_count = 0

                clock.tick(960)  # 16x processing speed (960 Hz) - matching Joy2Key 16x setting

        except KeyboardInterrupt:
            print("\nShutting down recoil controller...")
            self.running = False

        finally:
            pygame.quit()
            print("Recoil controller stopped.")

def main():
    """Main entry point"""
    print("Joy2Key Replacement - Recoil Controller")
    print("=" * 40)

    try:
        controller = RecoilController()
        controller.run()
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()