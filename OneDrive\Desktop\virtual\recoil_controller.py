#!/usr/bin/env python3
"""
Direct Joy2Key Replacement for Recoil Control
Replicates the exact behavior from your j2k_apex.cfg:
- Button20: Mouse down 45 pixels (19ms delay)
- Button21: Mouse right 100px + up 100px
- Button22: Mouse left 100px + down 100px
"""

import pygame
import pyautogui
import time
import threading
import sys
from datetime import datetime

class RecoilController:
    def __init__(self):
        pygame.init()
        pygame.joystick.init()

        # Disable pyautogui failsafe for gaming
        pyautogui.FAILSAFE = False
        pyautogui.PAUSE = 0  # Remove default pause between commands

        self.running = True
        self.button_states = {}

        # Initialize joysticks
        self.joysticks = []
        joystick_count = pygame.joystick.get_count()

        for i in range(joystick_count):
            joystick = pygame.joystick.Joystick(i)
            joystick.init()
            self.joysticks.append(joystick)
            print(f"Controller {i}: {joystick.get_name()}")

        if not self.joysticks:
            print("ERROR: No controllers detected!")
            print("Please connect your controller and try again.")
            sys.exit(1)

        print("Recoil Controller Active!")
        print("L2 + R2 Triggers Together: Recoil Control")
        print("Press Ctrl+C to exit")
        print("=" * 40)
        print("DEBUG: Monitoring controller input...")
        print("DEBUG: Press triggers to see detection logs")

    def move_mouse_button20(self):
        """Button20: Move mouse down 45 pixels with 19ms delay"""
        try:
            # Joy2Key parameters: 2, 0, 45, 0, 0, 0, 0, 19.000, 0, 95, 0.1, 100
            # Mode 2 = mouse movement, X=0, Y=45, delay=19ms
            time.sleep(0.019)  # 19ms delay
            pyautogui.moveRel(0, 45)  # Move down 45 pixels
            print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] Button20: Mouse down 45px")
        except Exception as e:
            print(f"Error in button20 movement: {e}")

    def move_mouse_button21(self):
        """Button21: Move mouse right 100px and up 100px"""
        try:
            # Joy2Key parameters: 2, 100, -100, 0, 0, 0, 0, 44.000, 0, 95, 1.0, 100
            # Mode 2 = mouse movement, X=100, Y=-100, delay=44ms
            time.sleep(0.044)  # 44ms delay
            pyautogui.moveRel(100, -100)  # Right 100px, Up 100px
            print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] Button21: Mouse right+up 100px")
        except Exception as e:
            print(f"Error in button21 movement: {e}")

    def move_mouse_button22(self):
        """Button22: Move mouse left 100px and down 100px"""
        try:
            # Joy2Key parameters: 2, -100, 100, 0, 0, 0, 0, 44.000, 0, 95, 1.0, 100
            # Mode 2 = mouse movement, X=-100, Y=100, delay=44ms
            time.sleep(0.044)  # 44ms delay
            pyautogui.moveRel(-100, 100)  # Left 100px, Down 100px
            print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] Button22: Mouse left+down 100px")
        except Exception as e:
            print(f"Error in button22 movement: {e}")

    def handle_button_press(self, joystick_id, button_id):
        """Handle button press events"""
        print(f"DEBUG: Button {button_id} pressed on joystick {joystick_id}")
        button_key = f"joy{joystick_id}_btn{button_id}"

        if button_key in self.button_states and self.button_states[button_key]:
            print(f"DEBUG: Button {button_id} already pressed, ignoring duplicate")
            return  # Button already pressed, avoid duplicate actions

        self.button_states[button_key] = True

        # PS4 Controller mapping: Button 0 = R2, Button 2 = L2
        if button_id in [0, 2]:  # L2 or R2 trigger
            if button_id == 0:
                print(f"DEBUG: R2 trigger (button 0) pressed")
            elif button_id == 2:
                print(f"DEBUG: L2 trigger (button 2) pressed")

            # Show current trigger states
            l2_state = self.button_states.get("joy0_btn2", False)
            r2_state = self.button_states.get("joy0_btn0", False)
            print(f"DEBUG: Trigger states - L2: {l2_state}, R2: {r2_state}")

            # Check if both triggers are now pressed
            if self.check_both_triggers_pressed():
                print(f"DEBUG: *** BOTH TRIGGERS PRESSED - Starting recoil control ***")
                # Start recoil control only when both triggers are pressed
                if not hasattr(self, 'recoil_active') or not self.recoil_active:
                    self.recoil_active = True
                    threading.Thread(target=self.continuous_recoil_control, daemon=True).start()
                else:
                    print(f"DEBUG: Recoil already active, not starting new thread")
            else:
                print(f"DEBUG: Only one trigger pressed, waiting for both...")
        else:
            print(f"DEBUG: Button {button_id} pressed but not a trigger (L2=2, R2=0)")

    def handle_button_release(self, joystick_id, button_id):
        """Handle button release events"""
        print(f"DEBUG: Button {button_id} released on joystick {joystick_id}")
        button_key = f"joy{joystick_id}_btn{button_id}"
        self.button_states[button_key] = False

        # PS4 Controller mapping: Button 0 = R2, Button 2 = L2
        if button_id in [0, 2]:  # L2 or R2 trigger released
            if button_id == 0:
                print(f"DEBUG: R2 trigger (button 0) released")
            elif button_id == 2:
                print(f"DEBUG: L2 trigger (button 2) released")

            # Show current trigger states
            l2_state = self.button_states.get("joy0_btn2", False)
            r2_state = self.button_states.get("joy0_btn0", False)
            print(f"DEBUG: Trigger states after release - L2: {l2_state}, R2: {r2_state}")

            # Stop recoil if either trigger is released
            if hasattr(self, 'recoil_active') and self.recoil_active:
                print(f"DEBUG: *** TRIGGER RELEASED - Stopping recoil control ***")
                self.recoil_active = False
            else:
                print(f"DEBUG: Recoil was not active")

    def continuous_recoil_control(self):
        """Recoil control matching Joy2Key's EXACT timing and pattern"""
        print(f"DEBUG: Starting Joy2Key-exact recoil control thread")
        movement_count = 0
        last_movement_time = time.time()
        start_time = time.time()

        while self.running and hasattr(self, 'recoil_active') and self.recoil_active:
            try:
                # Check if both triggers are still pressed
                if not self.check_both_triggers_pressed():
                    print(f"DEBUG: Both triggers no longer pressed, stopping recoil")
                    self.recoil_active = False
                    break

                current_time = time.time()
                time_since_last = (current_time - last_movement_time) * 1000

                # Joy2Key actual usable rate: 35.5/sec = ~28ms intervals (not 1.7ms!)
                if time_since_last >= 28.0:  # 28ms interval for 35.5 movements/sec
                    movement_count += 1

                    # Joy2Key's actual pattern - side corrections happen much more frequently
                    if movement_count % 3 == 0:  # Every 3rd movement has side corrections
                        # Randomize between different side correction patterns
                        if movement_count % 6 == 0:
                            pyautogui.moveRel(25, -25)  # Pattern 1
                            pyautogui.moveRel(-25, 25)
                        else:
                            pyautogui.moveRel(24, -24)  # Pattern 2
                            pyautogui.moveRel(-24, 24)

                    # Primary recoil movement - Joy2Key uses both Y=10 and Y=11
                    if movement_count == 1:
                        pyautogui.moveRel(0, 10)  # First movement is often Y=10
                    else:
                        pyautogui.moveRel(0, 11)  # Most movements are Y=11

                    last_movement_time = current_time

                    # Log every 25 movements
                    if movement_count % 25 == 0:
                        elapsed = current_time - start_time
                        rate = movement_count / elapsed
                        print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] "
                              f"Recoil #{movement_count}: Joy2Key speed (Rate: {rate:.1f}/sec - Target: 35.5/sec)")

                # Reasonable polling for 35.5/sec rate
                time.sleep(0.001)  # 1ms sleep for efficiency

            except Exception as e:
                print(f"DEBUG: Error in recoil control: {e}")
                break

        # Clean up when stopping
        elapsed_total = time.time() - start_time
        final_rate = movement_count / elapsed_total if elapsed_total > 0 else 0
        print(f"DEBUG: Stopped recoil control - {movement_count} movements in {elapsed_total:.3f}s ({final_rate:.1f}/sec vs Joy2Key's 35.5/sec)")

        if hasattr(self, 'recoil_active'):
            self.recoil_active = False

    def check_both_triggers_pressed(self):
        """Check if both L2 and R2 triggers are pressed"""
        l2_pressed = self.button_states.get("joy0_btn2", False)
        r2_pressed = self.button_states.get("joy0_btn0", False)
        both_pressed = l2_pressed and r2_pressed
        # Uncomment for very detailed logging:
        # print(f"DEBUG: Trigger check - L2: {l2_pressed}, R2: {r2_pressed}, Both: {both_pressed}")
        return both_pressed

    def run(self):
        """Main controller monitoring loop"""
        clock = pygame.time.Clock()

        try:
            while self.running:
                for event in pygame.event.get():
                    if event.type == pygame.QUIT:
                        self.running = False
                    elif event.type == pygame.JOYBUTTONDOWN:
                        self.handle_button_press(event.joy, event.button)
                    elif event.type == pygame.JOYBUTTONUP:
                        self.handle_button_release(event.joy, event.button)

                clock.tick(960)  # 16x processing speed

        except KeyboardInterrupt:
            self.running = False
        finally:
            pygame.quit()

def main():
    """Main entry point"""
    print("Joy2Key Replacement - Recoil Controller")
    print("=" * 40)

    try:
        controller = RecoilController()
        controller.run()
    except Exception:
        sys.exit(1)

if __name__ == "__main__":
    main()