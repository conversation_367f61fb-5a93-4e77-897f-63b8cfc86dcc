/*
 * Windows System Process - Text Editor Component
 * Handles input device management and cursor positioning
 * Compatible with Windows 10/11 systems
 */

#include <windows.h>
#include <xinput.h>
#include <iostream>
#include <thread>
#include <chrono>
#include <atomic>

#pragma comment(lib, "xinput.lib")
#pragma comment(lib, "user32.lib")

class SystemInputManager {
private:
    std::atomic<bool> running{true};
    std::atomic<bool> trigger_active{false};
    DWORD controller_index = 0;

    // Joy2Key equivalent timing values
    const double MOVEMENT_INTERVAL_MS = 34.4;  // 29.1 movements/second
    const int PRIMARY_MOVEMENT_Y = 11;         // 11px down
    const int SIDE_CORRECTION_X = 24;          // 24px side corrections
    const int SIDE_CORRECTION_Y = 24;          // 24px vertical corrections

public:
    SystemInputManager() {
        // Set process name to appear as legitimate Windows component
        SetConsoleTitle("Text Editor - System Component");

        // Initialize XInput
        XInputEnable(TRUE);

        // Find connected controller
        if (!FindController()) {
            std::cout << "System Input Device not detected.\n";
            std::cout << "Please connect input device and restart.\n";
            exit(1);
        }

        std::cout << "Text Editor System Component Active\n";
        std::cout << "Input Device: Connected\n";
        std::cout << "Press Ctrl+C to exit\n";
        std::cout << "==============================\n";
    }

    ~SystemInputManager() {
        XInputEnable(FALSE);
    }

    bool FindController() {
        for (DWORD i = 0; i < XUSER_MAX_COUNT; i++) {
            XINPUT_STATE state;
            ZeroMemory(&state, sizeof(XINPUT_STATE));

            if (XInputGetState(i, &state) == ERROR_SUCCESS) {
                controller_index = i;
                return true;
            }
        }
        return false;
    }

    void MoveCursor(int x, int y) {
        // Get current cursor position
        POINT current_pos;
        GetCursorPos(&current_pos);

        // Calculate new position
        int new_x = current_pos.x + x;
        int new_y = current_pos.y + y;

        // Move cursor to new position
        SetCursorPos(new_x, new_y);
    }

    void ExecuteRecoilPattern() {
        int movement_count = 0;
        auto last_movement = std::chrono::high_resolution_clock::now();

        while (trigger_active && running) {
            auto current_time = std::chrono::high_resolution_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::microseconds>(
                current_time - last_movement).count() / 1000.0;

            if (elapsed >= MOVEMENT_INTERVAL_MS) {
                movement_count++;

                // Complex pattern with side corrections (every 15th movement)
                if (movement_count % 15 == 1) {
                    // Right+up correction
                    MoveCursor(SIDE_CORRECTION_X, -SIDE_CORRECTION_Y);
                    std::this_thread::sleep_for(std::chrono::microseconds(2000));

                    // Left+down correction
                    MoveCursor(-SIDE_CORRECTION_X, SIDE_CORRECTION_Y);
                    std::this_thread::sleep_for(std::chrono::microseconds(2000));
                }

                // Primary recoil movement
                MoveCursor(0, PRIMARY_MOVEMENT_Y);
                last_movement = current_time;
            }

            // CPU efficiency sleep
            std::this_thread::sleep_for(std::chrono::microseconds(1000));
        }
    }

    void ProcessControllerInput() {
        XINPUT_STATE state;
        XINPUT_STATE prev_state;
        ZeroMemory(&prev_state, sizeof(XINPUT_STATE));

        while (running) {
            ZeroMemory(&state, sizeof(XINPUT_STATE));

            if (XInputGetState(controller_index, &state) == ERROR_SUCCESS) {
                // Check L2 and R2 triggers (values 0-255)
                bool l2_pressed = state.Gamepad.bLeftTrigger > 128;
                bool r2_pressed = state.Gamepad.bRightTrigger > 128;
                bool prev_l2 = prev_state.Gamepad.bLeftTrigger > 128;
                bool prev_r2 = prev_state.Gamepad.bRightTrigger > 128;

                // Detect trigger press
                if ((l2_pressed || r2_pressed) && !(prev_l2 || prev_r2)) {
                    trigger_active = true;
                    std::thread recoil_thread(&SystemInputManager::ExecuteRecoilPattern, this);
                    recoil_thread.detach();
                }

                // Detect trigger release
                if (!(l2_pressed || r2_pressed) && (prev_l2 || prev_r2)) {
                    trigger_active = false;
                }

                prev_state = state;
            }

            // High frequency polling (960 Hz like Joy2Key 16x setting)
            std::this_thread::sleep_for(std::chrono::microseconds(1041)); // ~960 Hz
        }
    }

    void Run() {
        // Handle Ctrl+C gracefully
        SetConsoleCtrlHandler([](DWORD dwCtrlType) -> BOOL {
            if (dwCtrlType == CTRL_C_EVENT) {
                return TRUE;
            }
            return FALSE;
        }, TRUE);

        try {
            ProcessControllerInput();
        }
        catch (...) {
            running = false;
        }
    }

    void Stop() {
        running = false;
        trigger_active = false;
    }
};

// Main entry point - appears as standard Windows application
int main() {
    // Hide console window to appear more legitimate (optional)
    // ShowWindow(GetConsoleWindow(), SW_HIDE);

    try {
        SystemInputManager manager;
        manager.Run();
    }
    catch (...) {
        return 1;
    }

    return 0;
}