@echo off
echo Building Windows System Component...
echo ====================================

REM Check for available compilers
echo Checking for available compilers...

REM Try Visual Studio compiler first
where cl >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Found: Visual Studio Build Tools
    echo Using Visual Studio compiler...
    cl /EHsc /O2 recoil_controller.cpp /Fe:notepad.exe /link xinput.lib user32.lib
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo SUCCESS: Built as notepad.exe
        echo File appears as: Windows Notepad Text Editor
        echo.
        goto :success
    ) else (
        echo Visual Studio compilation failed, trying MinGW...
    )
)

REM Try MinGW compiler
where g++ >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Found: MinGW-w64 in PATH
    echo Using MinGW compiler...
    g++ -std=c++11 -O2 -static recoil_controller.cpp -o notepad.exe -lxinput -luser32
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo SUCCESS: Built as notepad.exe
        echo File appears as: Windows Notepad Text Editor
        echo.
        goto :success
    ) else (
        echo MinGW compilation failed
        echo Trying without static linking...
        g++ -std=c++11 -O2 recoil_controller.cpp -o notepad.exe -lxinput -luser32
        if %ERRORLEVEL% EQU 0 (
            echo.
            echo SUCCESS: Built as notepad.exe (dynamic linking)
            echo File appears as: Windows Notepad Text Editor
            echo.
            goto :success
        )
    )
) else (
    echo MinGW not found in PATH
)

echo.
echo ERROR: No suitable compiler found or compilation failed
echo.
echo Please ensure you have either:
echo 1. Visual Studio Build Tools installed
echo 2. MinGW-w64 installed and added to PATH
echo.
echo Current PATH includes:
echo %PATH%
echo.
pause
exit /b 1

:success
echo Alternative executable names you can rename to:
echo - explorer.exe ^(Windows Explorer^)
echo - svchost.exe ^(Windows Service Host^)
echo - dwm.exe ^(Desktop Window Manager^)
echo - winlogon.exe ^(Windows Logon Process^)
echo.
echo WARNING: Only rename for legitimate testing purposes
echo.
pause