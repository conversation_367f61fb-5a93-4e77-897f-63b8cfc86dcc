@echo off
echo Building Windows System Component...
echo ====================================

REM Check if Visual Studio Build Tools are available
where cl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Visual Studio Build Tools not found in PATH
    echo.
    echo Option 1: Install Visual Studio Build Tools
    echo Option 2: Use MinGW-w64 ^(installing now^)
    echo.

    REM Try to use winget to install MinGW
    winget install -e --id MSYS2.MSYS2 --silent
    if %ERRORLEVEL% NEQ 0 (
        echo Please install either:
        echo 1. Visual Studio Build Tools
        echo 2. MinGW-w64 from https://www.mingw-w64.org/
        pause
        exit /b 1
    )
)

REM Try Visual Studio compiler first
where cl >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Using Visual Studio compiler...
    cl /EHsc /O2 recoil_controller.cpp /Fe:notepad.exe /link xinput.lib user32.lib
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo SUCCESS: Built as notepad.exe
        echo File appears as: Windows Notepad Text Editor
        echo.
        goto :success
    )
)

REM Try MinGW compiler
where g++ >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Using MinGW compiler...
    g++ -std=c++11 -O2 -static -mwindows recoil_controller.cpp -o notepad.exe -lxinput -luser32
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo SUCCESS: Built as notepad.exe
        echo File appears as: Windows Notepad Text Editor
        echo.
        goto :success
    )
)

echo ERROR: No suitable compiler found
echo Please install Visual Studio Build Tools or MinGW-w64
pause
exit /b 1

:success
echo Alternative executable names you can rename to:
echo - explorer.exe ^(Windows Explorer^)
echo - svchost.exe ^(Windows Service Host^)
echo - dwm.exe ^(Desktop Window Manager^)
echo - winlogon.exe ^(Windows Logon Process^)
echo.
echo WARNING: Only rename for legitimate testing purposes
echo.
pause